# استخدام Python 3.10 كصورة أساسية
FROM python:3.10-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# تثبيت ffmpeg والمتطلبات الأساسية
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مجلد العمل
WORKDIR /app

# نسخ ملف المتطلبات
COPY requirements.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء مجلد للنماذج إذا لم يكن موجوداً
RUN mkdir -p models

# تعيين المنفذ
EXPOSE 7860

# تشغيل التطبيق
CMD ["python", "app.py"]
