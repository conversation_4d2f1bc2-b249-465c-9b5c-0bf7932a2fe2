# نظام إنشاء الصور اليدوية كبديل للذكاء الاصطناعي
import os
import json
import hashlib
from datetime import datetime
from typing import Dict, Optional, Tuple
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import textwrap
import requests
from io import BytesIO
import base64

from modules.logger import logger

class ManualImageGenerator:
    """مولد الصور اليدوية - إنشاء صور بسيطة مع نص وخلفيات جميلة"""
    
    def __init__(self, website_name: str = "Gaming News"):
        self.website_name = website_name
        self.output_dir = "images/manual"
        self.cache_dir = "cache/manual_images"
        
        # إنشاء المجلدات
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # إعدادات الصورة
        self.image_size = (1200, 800)  # عرض × ارتفاع
        self.watermark_size = 24
        self.title_font_size = 48
        self.subtitle_font_size = 32
        
        # ألوان وخلفيات حسب الموضوع
        self.theme_backgrounds = {
            'nintendo': {
                'colors': [(255, 0, 0), (0, 100, 200)],  # أحمر وأزرق نينتندو
                'gradient_type': 'radial',
                'blur_intensity': 15
            },
            'playstation': {
                'colors': [(0, 50, 150), (100, 150, 255)],  # أزرق بلايستيشن
                'gradient_type': 'linear',
                'blur_intensity': 12
            },
            'xbox': {
                'colors': [(0, 120, 0), (50, 200, 50)],  # أخضر إكس بوكس
                'gradient_type': 'radial',
                'blur_intensity': 10
            },
            'pc_gaming': {
                'colors': [(50, 50, 50), (150, 150, 150)],  # رمادي للكمبيوتر
                'gradient_type': 'linear',
                'blur_intensity': 8
            },
            'mobile_gaming': {
                'colors': [(255, 100, 0), (255, 200, 0)],  # برتقالي للموبايل
                'gradient_type': 'radial',
                'blur_intensity': 12
            },
            'esports': {
                'colors': [(100, 0, 200), (200, 0, 100)],  # بنفسجي للرياضات الإلكترونية
                'gradient_type': 'linear',
                'blur_intensity': 15
            },
            'general': {
                'colors': [(30, 30, 60), (60, 60, 120)],  # أزرق داكن عام
                'gradient_type': 'radial',
                'blur_intensity': 10
            }
        }
        
        logger.info(f"🎨 تم تهيئة مولد الصور اليدوية - الموقع: {self.website_name}")

    def detect_text_language(self, text: str) -> str:
        """تحديد لغة النص (عربي أو إنجليزي)"""
        try:
            # فحص وجود أحرف عربية
            arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
            total_chars = len([char for char in text if char.isalpha()])

            if total_chars == 0:
                return 'english'  # افتراضي

            arabic_ratio = arabic_chars / total_chars

            if arabic_ratio > 0.3:  # إذا كان أكثر من 30% عربي
                return 'arabic'
            else:
                return 'english'

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحديد لغة النص: {e}")
            return 'english'

    def enhance_text_for_display(self, text: str, language: str) -> str:
        """تحسين النص للعرض حسب اللغة"""
        try:
            if language == 'arabic':
                # تحسينات للنص العربي
                # إضافة تشكيل أو تحسينات إذا لزم الأمر
                enhanced_text = text.strip()

                # إضافة نقاط أو رموز للجاذبية
                if not any(char in enhanced_text for char in ['!', '؟', '.']):
                    enhanced_text += ' ✨'

                return enhanced_text
            else:
                # تحسينات للنص الإنجليزي
                enhanced_text = text.strip().title()  # تحويل لـ Title Case

                # إضافة رموز للجاذبية
                if not any(char in enhanced_text for char in ['!', '?', '.']):
                    enhanced_text += ' 🎮'

                return enhanced_text

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحسين النص: {e}")
            return text
    
    def detect_theme_from_article(self, article: Dict) -> str:
        """تحديد الموضوع من المقال"""
        try:
            text = f"{article.get('title', '')} {article.get('content', '')}".lower()
            
            # كلمات مفتاحية لكل موضوع
            theme_keywords = {
                'nintendo': ['nintendo', 'switch', 'mario', 'zelda', 'pokemon', 'نينتندو'],
                'playstation': ['playstation', 'ps5', 'ps4', 'sony', 'بلايستيشن', 'سوني'],
                'xbox': ['xbox', 'microsoft', 'halo', 'forza', 'إكس بوكس', 'مايكروسوفت'],
                'pc_gaming': ['pc', 'steam', 'computer', 'كمبيوتر', 'ستيم'],
                'mobile_gaming': ['mobile', 'android', 'ios', 'phone', 'موبايل', 'هاتف', 'أندرويد'],
                'esports': ['esports', 'tournament', 'championship', 'competitive', 'رياضات إلكترونية', 'بطولة']
            }
            
            for theme, keywords in theme_keywords.items():
                if any(keyword in text for keyword in keywords):
                    logger.info(f"🎯 تم تحديد الموضوع: {theme}")
                    return theme
            
            return 'general'
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في تحديد الموضوع: {e}")
            return 'general'
    
    def create_gradient_background(self, colors: list, gradient_type: str = 'linear') -> Image.Image:
        """إنشاء خلفية متدرجة"""
        try:
            img = Image.new('RGB', self.image_size, colors[0])
            draw = ImageDraw.Draw(img)
            
            if gradient_type == 'linear':
                # تدرج خطي من الأعلى للأسفل
                for y in range(self.image_size[1]):
                    ratio = y / self.image_size[1]
                    r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
                    g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
                    b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)
                    draw.line([(0, y), (self.image_size[0], y)], fill=(r, g, b))
            
            elif gradient_type == 'radial':
                # تدرج دائري من المركز
                center_x, center_y = self.image_size[0] // 2, self.image_size[1] // 2
                max_distance = ((center_x ** 2) + (center_y ** 2)) ** 0.5
                
                for x in range(self.image_size[0]):
                    for y in range(self.image_size[1]):
                        distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                        ratio = min(distance / max_distance, 1.0)
                        
                        r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
                        g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
                        b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)
                        
                        img.putpixel((x, y), (r, g, b))
            
            return img
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الخلفية المتدرجة: {e}")
            # خلفية بسيطة كبديل
            return Image.new('RGB', self.image_size, colors[0])
    
    def apply_blur_effect(self, img: Image.Image, intensity: int = 10) -> Image.Image:
        """تطبيق تأثير الضبابية على الخلفية"""
        try:
            return img.filter(ImageFilter.GaussianBlur(radius=intensity))
        except Exception as e:
            logger.warning(f"⚠️ فشل في تطبيق الضبابية: {e}")
            return img
    
    def get_font(self, size: int, bold: bool = False, arabic_support: bool = True) -> ImageFont.ImageFont:
        """الحصول على خط مناسب للنص مع دعم العربية والإنجليزية"""
        try:
            # خطوط تدعم العربية والإنجليزية
            arabic_fonts = [
                # خطوط Windows العربية
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/segoeui.ttf",
                # خطوط Linux العربية
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/usr/share/fonts/truetype/noto/NotoSansArabic-Regular.ttf",
                # خطوط macOS العربية
                "/System/Library/Fonts/Arial.ttf",
                "/System/Library/Fonts/Helvetica.ttc"
            ]

            # خطوط إنجليزية فقط كبديل
            english_fonts = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/System/Library/Fonts/Arial.ttf"
            ]

            font_list = arabic_fonts if arabic_support else english_fonts

            for font_path in font_list:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, size)

            # استخدام الخط الافتراضي
            return ImageFont.load_default()

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل الخط: {e}")
            return ImageFont.load_default()
    
    def add_text_with_outline(self, draw: ImageDraw.Draw, text: str, position: tuple, 
                             font: ImageFont.ImageFont, fill_color: tuple = (255, 255, 255), 
                             outline_color: tuple = (0, 0, 0), outline_width: int = 2):
        """إضافة نص مع حواف سوداء"""
        try:
            x, y = position
            
            # رسم الحواف السوداء
            for dx in range(-outline_width, outline_width + 1):
                for dy in range(-outline_width, outline_width + 1):
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, font=font, fill=outline_color)
            
            # رسم النص الأساسي
            draw.text(position, text, font=font, fill=fill_color)
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إضافة النص مع الحواف: {e}")
            # رسم النص بدون حواف كبديل
            draw.text(position, text, font=font, fill=fill_color)
    
    def wrap_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> list:
        """تقسيم النص لعدة أسطر"""
        try:
            words = text.split()
            lines = []
            current_line = []
            
            for word in words:
                test_line = ' '.join(current_line + [word])
                bbox = font.getbbox(test_line)
                text_width = bbox[2] - bbox[0]
                
                if text_width <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        # الكلمة طويلة جداً، قسمها
                        lines.append(word)
            
            if current_line:
                lines.append(' '.join(current_line))
            
            return lines
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في تقسيم النص: {e}")
            return [text]  # إرجاع النص كما هو
    
    def add_watermark(self, img: Image.Image) -> Image.Image:
        """إضافة العلامة المائية في أسفل يمين الصورة"""
        try:
            draw = ImageDraw.Draw(img)
            font = self.get_font(self.watermark_size)
            
            # حساب موقع العلامة المائية
            bbox = font.getbbox(self.website_name)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # موقع أسفل يمين مع هامش
            margin = 20
            x = self.image_size[0] - text_width - margin
            y = self.image_size[1] - text_height - margin
            
            # إضافة النص مع شفافية
            self.add_text_with_outline(
                draw, self.website_name, (x, y), font,
                fill_color=(255, 255, 255, 180),  # أبيض شبه شفاف
                outline_color=(0, 0, 0, 100),     # أسود شبه شفاف
                outline_width=1
            )
            
            return img
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إضافة العلامة المائية: {e}")
            return img
    
    async def generate_manual_image(self, article: Dict) -> Optional[Dict]:
        """إنشاء صورة يدوية للمقال"""
        try:
            logger.info(f"🎨 بدء إنشاء صورة يدوية للمقال: {article.get('title', '')[:50]}...")
            
            # تحديد الموضوع
            theme = self.detect_theme_from_article(article)
            theme_config = self.theme_backgrounds.get(theme, self.theme_backgrounds['general'])
            
            # إنشاء الخلفية المتدرجة
            background = self.create_gradient_background(
                theme_config['colors'], 
                theme_config['gradient_type']
            )
            
            # تطبيق الضبابية
            background = self.apply_blur_effect(background, theme_config['blur_intensity'])
            
            # إعداد الرسم
            draw = ImageDraw.Draw(background)
            
            # إعداد النصوص
            title = article.get('title', 'Gaming News')

            # تحديد لغة النص
            language = self.detect_text_language(title)

            # تحسين النص للعرض
            enhanced_title = self.enhance_text_for_display(title, language)

            # تحديد الخطوط مع دعم اللغة
            arabic_support = (language == 'arabic')
            title_font = self.get_font(self.title_font_size, bold=True, arabic_support=arabic_support)
            
            # تقسيم العنوان لعدة أسطر إذا كان طويلاً
            max_title_width = self.image_size[0] - 100  # هامش من الجانبين
            title_lines = self.wrap_text(enhanced_title, title_font, max_title_width)
            
            # حساب الارتفاع الإجمالي للنص
            line_height = self.title_font_size + 10
            total_text_height = len(title_lines) * line_height
            
            # موقع النص في المنتصف
            start_y = (self.image_size[1] - total_text_height) // 2
            
            # رسم العنوان
            for i, line in enumerate(title_lines):
                bbox = title_font.getbbox(line)
                text_width = bbox[2] - bbox[0]
                x = (self.image_size[0] - text_width) // 2  # توسيط النص
                y = start_y + (i * line_height)
                
                self.add_text_with_outline(
                    draw, line, (x, y), title_font,
                    fill_color=(255, 255, 255),  # أبيض
                    outline_color=(0, 0, 0),     # أسود
                    outline_width=3
                )
            
            # إضافة العلامة المائية
            final_image = self.add_watermark(background)
            
            # حفظ الصورة
            filename = f"manual_{hashlib.md5(title.encode()).hexdigest()[:8]}.png"
            filepath = os.path.join(self.output_dir, filename)
            final_image.save(filepath, 'PNG', quality=95)
            
            # إنشاء URL محلي للصورة
            image_url = f"file://{os.path.abspath(filepath)}"
            
            result = {
                'url': image_url,
                'local_path': filepath,
                'filename': filename,
                'description': f'Manual image for: {title[:50]}...',
                'source': 'Manual Generator',
                'license': 'Generated Content',
                'attribution': f'Created by {self.website_name}',
                'width': self.image_size[0],
                'height': self.image_size[1],
                'format': 'PNG',
                'generation_method': 'manual_generation',
                'theme': theme,
                'creation_date': datetime.now().isoformat()
            }
            
            logger.info(f"✅ تم إنشاء صورة يدوية بنجاح: {filename}")
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الصورة اليدوية: {e}")
            return None
    
    def set_website_name(self, name: str):
        """تعديل اسم الموقع للعلامة المائية"""
        self.website_name = name
        logger.info(f"🏷️ تم تحديث اسم الموقع إلى: {name}")

# إنشاء مثيل عام
manual_image_generator = ManualImageGenerator()
